package com.example.banking.api.service.process.operations;

import com.example.banking.api.model.BankingUser;
import com.example.banking.api.service.process.ProcessCommunication;
import com.example.banking.api.service.process.ProcessOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Session-based authentication operation that authenticates a user and leaves
 * the process in the banking menu state for subsequent session operations.
 * Unlike UserAuthenticationOperation, this does NOT perform logout after authentication.
 */
public class SessionAuthenticationOperation implements ProcessOperation<BankingUser> {
    
    private static final Logger logger = LoggerFactory.getLogger(SessionAuthenticationOperation.class);
    
    private final String username;
    private final String password;
    
    public SessionAuthenticationOperation(String username, String password) {
        this.username = username;
        this.password = password;
    }
    
    @Override
    public BankingUser execute(ProcessCommunication communication) throws Exception {
        logger.info("=== SESSION AUTHENTICATION OPERATION START ===");
        
        // Wait for initial menu
        String initialOutput = communication.waitForInitialMenu();
        if (!initialOutput.toLowerCase().contains("login") && !initialOutput.toLowerCase().contains("choose")) {
            logger.error("No menu found in initial output");
            return null;
        }
        
        // Perform authentication
        String authResult = communication.authenticateUser(username, password);
        
        // Analyze the result
        boolean isSuccessful = communication.isAuthenticationSuccessful(authResult, username);
        logger.info("Authentication successful: {}", isSuccessful);
        
        if (isSuccessful) {
            // DO NOT perform logout - leave the process in the banking menu state
            // This allows subsequent session operations to work with the authenticated process
            logger.info("=== SESSION AUTHENTICATION OPERATION END - SUCCESS (STAYING LOGGED IN) ===");
            return new BankingUser(username, 0.0);
        } else {
            // Handle failed login
            communication.performGracefulExit();
            
            logger.info("=== SESSION AUTHENTICATION OPERATION END - FAILURE ===");
            return null;
        }
    }
}
